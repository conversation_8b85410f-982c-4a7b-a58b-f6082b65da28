package it.yolo.v3;

import io.quarkus.test.junit.QuarkusTest;
import io.restassured.http.ContentType;
import org.junit.jupiter.api.Test;

import static io.restassured.RestAssured.given;
import static org.hamcrest.CoreMatchers.is;
import static org.hamcrest.CoreMatchers.notNullValue;

/**
 * Test di integrazione per l'API V3 di emissione.
 */
@QuarkusTest
public class EmissionV3IntegrationTest {
    
    @Test
    public void testEmissionEndpoint() {
        String orderCode = "TEST_ORDER_001";
        
        // Test POST /api/v3/emissions/{orderCode}
        given()
            .header("Authorization", "Bearer test-token")
            .contentType(ContentType.JSON)
            .body("{}")
            .when()
            .post("/api/v3/emissions/" + orderCode)
            .then()
            .statusCode(201)
            .header("Location", notNullValue())
            .body("orderCode", is(orderCode))
            .body("policyNumber", notNullValue());
    }
    
    @Test
    public void testStatusEndpoint() {
        String orderCode = "TEST_ORDER_002";
        
        // Prima emetti una polizza
        given()
            .header("Authorization", "Bearer test-token")
            .contentType(ContentType.JSON)
            .body("{}")
            .when()
            .post("/api/v3/emissions/" + orderCode)
            .then()
            .statusCode(201);
        
        // Poi verifica lo stato
        given()
            .when()
            .get("/api/v3/emissions/" + orderCode + "/status")
            .then()
            .statusCode(200)
            .body("orderCode", is(orderCode))
            .body("state", notNullValue())
            .body("updatedAt", notNullValue());
    }
    
    @Test
    public void testEmissionWithoutToken() {
        String orderCode = "TEST_ORDER_003";
        
        given()
            .contentType(ContentType.JSON)
            .body("{}")
            .when()
            .post("/api/v3/emissions/" + orderCode)
            .then()
            .statusCode(401);
    }
    
    @Test
    public void testStatusNotFound() {
        given()
            .when()
            .get("/api/v3/emissions/NON_EXISTENT_ORDER/status")
            .then()
            .statusCode(404);
    }
    
    @Test
    public void testHealthCheck() {
        given()
            .when()
            .get("/q/health/ready")
            .then()
            .statusCode(200)
            .body("status", is("UP"));
    }
}
