package it.yolo.v3.domain;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * Modello di dominio per la Polizza emessa nella V3.
 * Rappresenta il risultato dell'operazione di emissione.
 */
public record Policy(
    String id,
    String policyNumber,
    String orderCode,
    String customerId,
    String productId,
    String state,
    BigDecimal premium,
    LocalDateTime startDate,
    LocalDateTime endDate,
    LocalDateTime emissionDate
) {
    
    public Policy {
        if (policyNumber == null || policyNumber.isBlank()) {
            throw new IllegalArgumentException("Policy number cannot be null or blank");
        }
        if (orderCode == null || orderCode.isBlank()) {
            throw new IllegalArgumentException("Order code cannot be null or blank");
        }
    }
    
    /**
     * Verifica se la polizza è stata emessa con successo
     */
    public boolean isEmitted() {
        return "EMITTED".equalsIgnoreCase(state) || "ACTIVE".equalsIgnoreCase(state);
    }
    
    /**
     * Verifica se la polizza è valida
     */
    public boolean isValid() {
        return policyNumber != null && !policyNumber.isBlank() &&
               emissionDate != null &&
               startDate != null &&
               endDate != null &&
               startDate.isBefore(endDate);
    }
}
