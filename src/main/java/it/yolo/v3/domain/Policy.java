package it.yolo.v3.domain;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.JsonNode;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <PERSON>lo di dominio per la Polizza emessa nella V3.
 * Rappresenta il risultato dell'operazione di emissione.
 */
public record Policy(
    Data data;
) {
    public record Data(
            @JsonProperty("id") Long id,
            @JsonProperty("policyCode") String policyCode,
            @JsonProperty("startDate") String startDate,
            @JsonProperty("endDate") String endDate,
            @JsonProperty("customer") Customer customer,
            @JsonProperty("product") Product product,
            @JsonProperty("orderId") Long orderId,
            @JsonProperty("externalCode") String externalCode,
            @JsonProperty("originalPolicyNumber") String originalPolicyNumber,
            @JsonProperty("state") JsonNode state,
            @JsonProperty("payment") JsonNode payment,
            @JsonProperty("choosenProperties") JsonNode choosenProperties,
            @JsonProperty("insurancePremium") BigDecimal insurancePremium,
            @JsonProperty("warranties") List<Object> warranties,
            @JsonProperty("additionalPolicyInfo") JsonNode additionalPolicyInfo,
            @JsonProperty("createdAt") String createdAt,
            @JsonProperty("updatedAt") String updatedAt,
            @JsonProperty("quantity") Long quantity,
            @JsonProperty("insuredIsContractor") Boolean insuredIsContractor,
            @JsonProperty("certificateFileName") String certificateFileName,
            @JsonProperty("certificateLink") String certificateLink,
            @JsonProperty("certificateContentType") String certificateContentType,
            @JsonProperty("certificateFileSize") String certificateFileSize,
            @JsonProperty("certificateUpdatedAt") String certificateUpdatedAt,
            @JsonProperty("certificate") String certificate,
            @JsonProperty("type") String type,
            @JsonProperty("withdrawalRequestDate") String withdrawalRequestDate,
            @JsonProperty("renewedAt") String renewedAt,
            @JsonProperty("markedAsRenewable") Boolean markedAsRenewable,
            @JsonProperty("policySubstitution") Boolean policySubstitution,
            @JsonProperty("masterPolicyNumber") String masterPolicyNumber,
            @JsonProperty("nextBillingDate") String nextBillingDate,
            @JsonProperty("name") String name,
            @JsonProperty("username") String username,
            @JsonProperty("password") String password,
            @JsonProperty("isWithdrawable") Boolean isWithdrawable,
            @JsonProperty("isDeactivable") Boolean isDeactivable,
            @JsonProperty("subscriptionId") String subscriptionId
    ) {
    }
}
