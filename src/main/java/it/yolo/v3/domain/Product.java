package it.yolo.v3.domain;

import com.fasterxml.jackson.annotation.JsonAnySetter;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.JsonNode;
import it.yolo.client.dto.iad.response.*;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * Modello di dominio per il Prodotto che rappresenta la struttura completa della risposta JSON.
 * Mantiene la logica di dominio esistente per l'emissione.
 */
public record Product(
        @JsonProperty("data") Data data,
        @JsonAnySetter Map<String, Object> additionalProperties
) {
    public record Data(
            @JsonProperty("id") Integer id,
            @JsonProperty("code") String code,
            @JsonProperty("description") String description,
            @JsonProperty("startDate") String startDate,
            @JsonProperty("recurring") Boolean recurring,
            @JsonProperty("splits") List<Object> splits,
            @JsonProperty("questions") List<Question> questions,
            @JsonProperty("categories") List<Category> categories,
            @JsonProperty("insuranceCompany") String insuranceCompany,
            @JsonProperty("price") String price,
            @JsonProperty("short_description") String shortDescription,
            @JsonProperty("images") ImagesProduct images,
            @JsonProperty("holder_maximum_age") Integer holderMaximumAge,
            @JsonProperty("business") Boolean business,
            @JsonProperty("display_price") String displayPrice,
            @JsonProperty("show_in_dashboard") Boolean showInDashboard,
            @JsonProperty("maximum_insurable") Integer maximumInsurable,
            @JsonProperty("only_contractor") Boolean onlyContractor,
            @JsonProperty("conditions_package") String conditionsPackage,
            @JsonProperty("packets") List<Packet> packets,
            @JsonProperty("insuranceCompanyLogo") String insuranceCompanyLogo,
            @JsonProperty("catalogId") Integer catalogId,
            @JsonProperty("holder_minimum_age") Integer holderMinimumAge,
            @JsonProperty("information_package") String informationPackage,
            @JsonProperty("can_open_claim") Boolean canOpenClaim,
            @JsonProperty("conditions") String conditions,
            @JsonProperty("productDescription") String productDescription,
            @JsonProperty("titleProd") Object titleProd,
            @JsonProperty("planId") String planId,
            @JsonProperty("planName") String planName,
            @JsonProperty("quotatorType") String quotatorType,
            @JsonProperty("productsPaymentMethods") List<ProductPaymentMethod> productPaymentMethod,
            @JsonProperty("legacy") JsonNode legacy,
            @JsonProperty("configuration") ProductConfiguration configuration,
            @JsonAnySetter Map<String, Object> additionalProperties
    ) {

        public record ProductConfiguration(
                @JsonProperty("canOpenClaim") Boolean canOpenClaim,
                @JsonProperty("certificate") String certificate,
                @JsonProperty("claimProvider") String claimProvider,
                @JsonProperty("claimType") String claimType,
                @JsonProperty("deactivationProvider") String deactivationProvider,
                @JsonProperty("deactivationType") String deactivationType,
                @JsonProperty("emission") String emission,
                @JsonProperty("emissionPrefix") String emissionPrefix,
                @JsonProperty("id") Integer id,
                @JsonProperty("properties") JsonNode properties,
                @JsonProperty("quotation") String quotation,
                @JsonProperty("withdrawType") String withdrawType
        ) {
        }

        public Data {
            Objects.requireNonNull(code, "Product code cannot be null");
            Objects.requireNonNull(insuranceCompany, "Insurance company cannot be null");
            if (code.isBlank()) {
                throw new IllegalArgumentException("Product code cannot be blank");
            }
            if (insuranceCompany.isBlank()) {
                throw new IllegalArgumentException("Insurance company cannot be blank");
            }
        }
    }

    public Product {
        Objects.requireNonNull(data, "Product data cannot be null");
    }

    // Metodi di dominio mantenuti per compatibilità
    public String id() {
        return data.id() != null ? String.valueOf(data.id()) : null;
    }

    public String code() {
        return data.code();
    }

    public String description() {
        return data.description();
    }

    public String insuranceCompany() {
        return data.insuranceCompany();
    }

    public BigDecimal price() {
        return data.price() != null ? new BigDecimal(data.price()) : null;
    }

    public String emissionType() {
        // Determina il tipo di emissione in base al quotatorType
        String quotator = data.configuration().emission();
        if (quotator == null) return "INTERNAL";

        return quotator.contains("EXTERNAL") ? "EXTERNAL" : "INTERNAL";
    }

    public String quotatorType() {
        return data.quotatorType();
    }

    public String business() {
        return data.business() != null ? data.business().toString() : null;
    }

    /**
     * Verifica se il prodotto usa emissione interna
     */
    public boolean isInternalEmission() {
        return "INTERNAL".equalsIgnoreCase(emissionType());
    }

    /**
     * Verifica se il prodotto usa emissione esterna
     */
    public boolean isExternalEmission() {
        return "EXTERNAL".equalsIgnoreCase(emissionType());
    }

    /**
     * Verifica se il prodotto è valido per l'emissione
     */
    public boolean isValidForEmission() {
        return code() != null && !code().isBlank() &&
                insuranceCompany() != null && !insuranceCompany().isBlank() &&
                (isInternalEmission() || isExternalEmission());
    }
}
