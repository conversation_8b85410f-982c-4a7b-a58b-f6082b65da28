package it.yolo.v3.domain;

import java.time.LocalDateTime;

/**
 * Modello di dominio semplificato per l'Ordine nella V3.
 * Contiene solo i campi essenziali per l'emissione.
 */

import com.fasterxml.jackson.annotation.JsonAnySetter;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.JsonNode;
import it.yolo.client.order.dto.response.OrderItem;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

public record Order(
        @JsonProperty("data") Data data,
        @JsonProperty("additionalInfo") Object additionalInfo,
        @JsonProperty("discount") String discount,
        @JsonProperty("version") String version,
        @JsonProperty("fieldToRecover") JsonNode fieldToRecover,
        @JsonAnySetter Map<String, Object> additionalProperties
) {

    public boolean isValidForEmission() {
        return data != null && data.productId != null && data.customerId != null;
    }

    public boolean hasConfirmedPayment() {
        return data != null && data.anagStates != null && data.anagStates.has("PAYMENT_CONFIRMED");
    }

    public record Data(
            @JsonProperty("id") Integer id,
            @JsonProperty("orderCode") String orderCode,
            @JsonProperty("productId") String productId,
            Object policyCode,
            JsonNode anagStates,
            @JsonProperty("packetId") Integer packetId,
            Product product,
            @JsonProperty("brokerId") Integer brokerId,
            @JsonProperty("companyId") Integer companyId,
            @JsonProperty("customerId") String customerId,
            Object choosenProperties,
            @JsonProperty("insurancePremium") Double insurancePremium,
            String createdBy,
            String updatedBy,
            @JsonProperty("fieldToRecover") JsonNode fieldToRecover,
            @JsonProperty("start_date") LocalDateTime startDate,
            @JsonProperty("expiration_date") LocalDateTime expirationDate,
            JsonNode survey,
            String createdAt,
            String updatedAt,
            JsonNode packet,
            JsonNode orderHistory,
            List<String> stepState,
            List<OrderItem> orderItem,
            Customer customer,
            String type,
            @JsonProperty("plan_id") String planId,
            @JsonProperty("plan_name") String planName,
            String paymentToken,
            String paymentTransactionId,
            String productType,
            @JsonProperty("paymentFrequency") String paymentType,
            String language,
            String parentOrder,
            String paymentProvider,
            String externalId,
            String utmSource,
            String agenziaDiRiferimento,
            Boolean intermediaryOrder,
            @JsonAnySetter Map<String, Object> additionalProperties
    ) {}
}
