package it.yolo.v3.domain;

/**
 * Modello di dominio semplificato per il Cliente nella V3.
 * Contiene solo i campi essenziali per l'emissione.
 */

import com.fasterxml.jackson.annotation.JsonAnySetter;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.JsonNode;
import java.util.Map;

public record Customer(
        @JsonProperty("data") Data data,
        @JsonAnySetter Map<String, Object> additionalProperties
) {

    public boolean isValidForEmission() {
        return data != null && data.taxCode != null && !data.taxCode.isBlank();
    }

    public record Data(
            @JsonProperty("id") Integer id,
            @JsonProperty("customer_code") String customerCode,
            @JsonProperty("external_code") JsonNode externalCode,
            Object username,
            String name,
            String surname,
            @JsonProperty("date_of_birth") String dateOfBirth,
            @JsonProperty("birth_city") String birthCity,
            @JsonProperty("birth_province") String birthProvince,
            @JsonProperty("tax_code") String taxCode,
            String gender,
            String street,
            @JsonProperty("street_number") String streetNumber,
            String city,
            String type,
            String country,
            @JsonProperty("zip_code") String zipCode,
            String province,
            @JsonProperty("primary_mail") String primaryMail,
            @JsonProperty("secondary_mail") String secondaryMail,
            @JsonProperty("primary_phone") String primaryPhone,
            @JsonProperty("secondary_phone") Object secondaryPhone,
            String language,
            @JsonProperty("legal_form") String legalForm,
            String createdAt,
            String updatedAt,
            @JsonProperty("state_abbr") String stateAbbr,
            String password,
            String company,
            @JsonProperty("birth_country") String birthCountry,
            @JsonProperty("birth_state") String birthState,
            String education,
            @JsonProperty("country_id") Integer countryId,
            @JsonProperty("state_id") Integer stateId,
            @JsonProperty("ndg") String ndg,
            String state,
            @JsonProperty("city_id") Integer cityId,
            @JsonProperty("birth_state_id") Integer birthStateId,
            @JsonProperty("birth_city_id") Integer birthCityId,
            @JsonProperty("birth_country_id") Integer birthCountryId,
            @JsonProperty("birth_state_abbr") String birthStateAbbr,
            @JsonAnySetter Map<String, Object> additionalProperties
    ) {}
}
