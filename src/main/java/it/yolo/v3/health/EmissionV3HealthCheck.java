package it.yolo.v3.health;

import it.yolo.v3.repository.EmissionStatusRepository;
import it.yolo.v3.strategy.EmissionStrategyFactory;
import org.eclipse.microprofile.health.HealthCheck;
import org.eclipse.microprofile.health.HealthCheckResponse;
import org.eclipse.microprofile.health.Readiness;

import javax.enterprise.context.ApplicationScoped;
import javax.inject.Inject;

/**
 * Health check per l'API V3 di emissione.
 * Verifica che tutti i componenti critici siano funzionanti.
 */
@Readiness
@ApplicationScoped
public class EmissionV3HealthCheck implements HealthCheck {
    
    @Inject
    EmissionStatusRepository statusRepository;
    
    @Inject
    EmissionStrategyFactory strategyFactory;
    
    @Override
    public HealthCheckResponse call() {
        try {
            // Verifica il repository di stato
            int statusCount = statusRepository.size();
            
            // Verifica le strategie disponibili
            String[] availableStrategies = strategyFactory.getAvailableStrategies();
            
            // Verifica che almeno le strategie base siano disponibili
            boolean hasInternalStrategy = strategyFactory.hasStrategy("INTERNAL");
            boolean hasExternalStrategy = strategyFactory.hasStrategy("EXTERNAL");
            
            if (!hasInternalStrategy || !hasExternalStrategy) {
                return HealthCheckResponse.down("emission-v3")
                    .withData("error", "Missing required emission strategies")
                    .withData("available_strategies", String.join(", ", availableStrategies))
                    .build();
            }
            
            return HealthCheckResponse.up("emission-v3")
                .withData("status_repository_size", statusCount)
                .withData("available_strategies", String.join(", ", availableStrategies))
                .withData("internal_strategy", hasInternalStrategy)
                .withData("external_strategy", hasExternalStrategy)
                .build();
                
        } catch (Exception e) {
            return HealthCheckResponse.down("emission-v3")
                .withData("error", e.getMessage())
                .build();
        }
    }
}
