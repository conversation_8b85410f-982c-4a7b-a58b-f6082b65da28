package it.yolo.v3.dto;

/**
 * <PERSON><PERSON> per l'emissione V3.
 * 
 * Per ora è vuota come da specifica, ma può essere estesa in futuro
 * per includere override specifici o parametri aggiuntivi.
 * 
 * Esempi di possibili estensioni future:
 * - Parametri di configurazione specifici
 * - Override di comportamenti default
 * - Metadati aggiuntivi per il processo
 */
public record EmissionRequest() {
    
    // Record vuoto come da specifica
    // Può essere esteso in futuro senza breaking changes
}
