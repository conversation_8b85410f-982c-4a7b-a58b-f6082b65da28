package it.yolo.v3.service;

import it.yolo.v3.domain.Policy;
import it.yolo.v3.service.CertificateService.Certificate;
import io.smallrye.mutiny.Uni;
import org.jboss.logging.Logger;

import javax.enterprise.context.ApplicationScoped;

/**
 * Servi<PERSON> per l'invio delle email di conferma emissione.
 */
@ApplicationScoped
public class EmailService {
    
    private static final Logger LOG = Logger.getLogger(EmailService.class);
    
    /**
     * Invia l'email di conferma emissione con certificato allegato.
     * 
     * @param policy Polizza emessa
     * @param certificate Certificato da allegare
     * @param token Token di autorizzazione
     * @return Uni che completa quando l'email è stata inviata
     */
    public Uni<Void> sendPolicyEmail(Policy policy, Certificate certificate, String token) {
        LOG.infof("Sending policy email for policy %s", policy.data().policyCode());
        
        return Uni.createFrom().item(() -> {
            // Simula l'invio dell'email
            try {
                Thread.sleep(500); // Simula latenza del servizio email
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                throw new RuntimeException("Email sending interrupted", e);
            }
            
            // Simula la composizione e invio dell'email
            String emailContent = composeEmailContent(policy, certificate);
            sendEmail(policy.data().customer().data().id(), "Conferma Emissione Polizza", emailContent, certificate);
            
            LOG.infof("Policy email sent successfully for policy %s", policy.data().policyCode());
            return null;
        });
    }
    
    private String composeEmailContent(Policy policy, Certificate certificate) {
        return String.format("""
            Gentile Cliente,
            
            La Sua polizza numero %s è stata emessa con successo.
            
            Dettagli polizza:
            - Numero: %s
            - Data emissione: %s
            - Data inizio copertura: %s
            - Data fine copertura: %s
            
            In allegato trova il certificato di polizza.
            
            Cordiali saluti,
            Il Team YOLO Insurance
            """, 
            policy.data().policyCode(),
            policy.data().policyCode(),
            policy.data().startDate(),
            policy.data().startDate(),
            policy.data().endDate()
        );
    }
    
    private void sendEmail(Integer customerId, String subject, String content, Certificate certificate) {
        // Simula l'invio tramite servizio esterno (es. SendGrid, AWS SES)
        LOG.debugf("Sending email to customer %s: %s", customerId, subject);
        
        // In una implementazione reale, qui ci sarebbe la chiamata al servizio di email
        // es. communicationManagerClient.sendEmail(...)
    }
}
