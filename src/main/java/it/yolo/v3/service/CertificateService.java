package it.yolo.v3.service;

import it.yolo.v3.domain.Policy;
import io.smallrye.mutiny.Uni;
import org.jboss.logging.Logger;

import javax.enterprise.context.ApplicationScoped;

/**
 * Servizio per la generazione dei certificati di polizza.
 */
@ApplicationScoped
public class CertificateService {
    
    private static final Logger LOG = Logger.getLogger(CertificateService.class);
    
    /**
     * Genera il certificato per una polizza emessa.
     * 
     * @param policy Polizza per cui generare il certificato
     * @param token Token di autorizzazione
     * @return Uni con il certificato generato
     */
    public Uni<Certificate> generate(Policy policy, String token) {
        LOG.infof("Generating certificate for policy %s", policy.data().policyCode());
        
        return Uni.createFrom().item(() -> {
            // Simula la generazione del certificato
            try {
                Thread.sleep(800); // Simula elaborazione complessa
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                throw new RuntimeException("Certificate generation interrupted", e);
            }
            
            Certificate certificate = new Certificate(
                "CERT_" + policy.data().policyCode(),
                policy.data().policyCode(),
                "PDF",
                generateCertificateContent(policy),
                java.time.LocalDateTime.now()
            );
            
            LOG.infof("Certificate generated successfully for policy %s", policy.data().policyCode());
            return certificate;
        });
    }
    
    private String generateCertificateContent(Policy policy) {
        // Simula la generazione del contenuto del certificato
        return "Certificate content for policy " + policy.data().policyCode();
    }
    
    /**
     * Record per rappresentare un certificato generato
     */
    public record Certificate(
        String id,
        String policyNumber,
        String format,
        String content,
        java.time.LocalDateTime generatedAt
    ) {}
}
