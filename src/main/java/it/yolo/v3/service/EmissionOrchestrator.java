package it.yolo.v3.service;

import it.yolo.v3.dto.EmissionResponse;
import io.smallrye.mutiny.Uni;
import jakarta.enterprise.context.ApplicationScoped;
import org.jboss.logging.Logger;

/**
 * Orchestratore per la fase sincrona del processo di emissione V3.
 * Coordina il recupero dei dati e l'emissione della polizza, delegando
 * le operazioni asincrone al BackgroundTaskRunner.
 * 
 * Implementazione completa nel Task 3.
 */
@ApplicationScoped
public class EmissionOrchestrator {
    
    private static final Logger LOG = Logger.getLogger(EmissionOrchestrator.class);
    
    /**
     * Avvia il processo di emissione sincrono.
     * 
     * @param orderCode Codice dell'ordine
     * @param token Token di autorizzazione
     * @return Uni con la risposta di emissione
     */
    public Uni<EmissionResponse> startEmissionProcess(String orderCode, String token) {
        LOG.infof("Starting emission process for order %s", orderCode);
        
        // TODO: Implementazione completa nel Task 3
        // Per ora restituiamo una risposta mock per testare l'endpoint
        return Uni.createFrom().item(() -> {
            // Simula una breve elaborazione
            try {
                Thread.sleep(100);
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
            }
            
            String mockPolicyNumber = "POL" + orderCode + System.currentTimeMillis();
            LOG.infof("Mock emission completed for order %s, policy: %s", orderCode, mockPolicyNumber);
            
            return new EmissionResponse(orderCode, mockPolicyNumber);
        });
    }
}
