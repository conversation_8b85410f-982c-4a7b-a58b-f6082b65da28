package it.yolo.v3.service;

import it.yolo.v3.domain.Policy;
import io.quarkus.virtual.threads.VirtualThreads;
import jakarta.enterprise.context.ApplicationScoped;
import org.jboss.logging.Logger;

/**
 * Gestore dei task in background per le operazioni post-emissione.
 * 
 * Implementazione stub per il Task 3.
 * Implementazione completa nel Task 4.
 */
@ApplicationScoped
public class BackgroundTaskRunner {
    
    private static final Logger LOG = Logger.getLogger(BackgroundTaskRunner.class);
    
    /**
     * Esegue i task post-emissione in background.
     * 
     * Questo metodo viene chiamato in modalità fire-and-forget
     * dopo che la polizza è stata emessa con successo.
     * 
     * @param policy Polizza emessa
     * @param token Token di autorizzazione
     */
    @VirtualThreads
    public void executePostEmissionTasks(Policy policy, String token) {
        LOG.infof("Starting background tasks for policy %s (order %s)", 
                 policy.policyNumber(), policy.orderCode());
        
        // TODO: Implementazione completa nel Task 4
        // Per ora simula solo l'esecuzione
        try {
            Thread.sleep(1000); // Simula elaborazione in background
            LOG.infof("Background tasks completed for policy %s", policy.policyNumber());
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            LOG.errorf(e, "Background tasks interrupted for policy %s", policy.policyNumber());
        }
    }
}
