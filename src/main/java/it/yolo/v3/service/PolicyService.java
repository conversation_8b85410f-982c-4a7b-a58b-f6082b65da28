package it.yolo.v3.service;

import it.yolo.v3.domain.Customer;
import it.yolo.v3.domain.Order;
import it.yolo.v3.domain.Policy;
import it.yolo.v3.domain.Product;
import it.yolo.v3.strategy.EmissionContext;
import it.yolo.v3.strategy.EmissionStrategy;
import it.yolo.v3.strategy.EmissionStrategyFactory;
import io.smallrye.mutiny.Uni;

import org.jboss.logging.Logger;

import javax.enterprise.context.ApplicationScoped;
import javax.inject.Inject;

/**
 * Servizio di dominio per la gestione delle polizze.
 *
 * Utilizza il Strategy Pattern per delegare l'emissione alla strategia appropriata
 * (interna o esterna) basandosi sulla configurazione del prodotto.
 */
@ApplicationScoped
public class PolicyService {

    private static final Logger LOG = Logger.getLogger(PolicyService.class);

    @Inject
    EmissionStrategyFactory strategyFactory;

    /**
     * Emette una polizza basata sui dati dell'ordine, cliente e prodotto.
     *
     * Questa è la logica critica che deve essere eseguita in modo sincrono.
     * Delega l'emissione effettiva alla strategia appropriata.
     *
     * @param order Dati dell'ordine
     * @param customer Dati del cliente
     * @param product Dati del prodotto
     * @param token Token di autorizzazione
     * @return Uni con la polizza emessa
     */
    public Uni<Policy> emit(Order order, Customer customer, Product product, String token) {
        LOG.infof("Starting policy emission for order %s, customer %s, product %s",
                 order.data().orderCode(), customer.data().id(), product.id());

        return Uni.createFrom().item(() -> {
            // Validazioni di business generali
            validateEmissionData(order, customer, product);

            // Crea il contesto per la strategia
            EmissionContext context = new EmissionContext(order, customer, product, token);

            // Seleziona la strategia appropriata
            EmissionStrategy strategy = strategyFactory.getStrategy(context);

            LOG.infof("Using emission strategy: %s for order %s",
                     strategy.getStrategyName(), order.data().orderCode());

            return strategy;
        })
        .flatMap(strategy -> {
            // Crea nuovamente il contesto (necessario per il flatMap)
            EmissionContext context = new EmissionContext(order, customer, product, token);

            // Delega l'emissione alla strategia selezionata
            return strategy.emit(context);
        })
        .onItem().invoke(policy ->
            LOG.infof("Policy emitted successfully: %s for order %s",
                     policy.data().policyCode(), order.data().orderCode())
        );
    }

    /**
     * Valida i dati necessari per l'emissione
     */
    private void validateEmissionData(Order order, Customer customer, Product product) {
        if (!order.isValidForEmission()) {
            throw new IllegalArgumentException("Order is not in a valid state for emission: " + order.data().anagStates().toString());
        }

        if (!customer.isValidForEmission()) {
            throw new IllegalArgumentException("Customer data is not valid for emission");
        }

        if (!product.isValidForEmission()) {
            throw new IllegalArgumentException("Product is not valid for emission");
        }

        if (!order.hasConfirmedPayment()) {
            throw new IllegalArgumentException("Order payment is not confirmed");
        }

        // Verifica che esista una strategia per il tipo di emissione del prodotto
        if (!strategyFactory.hasStrategy(product.emissionType())) {
            throw new IllegalArgumentException("No emission strategy available for type: " + product.emissionType());
        }
    }
}
