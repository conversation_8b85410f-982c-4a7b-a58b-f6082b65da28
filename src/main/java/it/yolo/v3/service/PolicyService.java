package it.yolo.v3.service;

import it.yolo.v3.domain.Customer;
import it.yolo.v3.domain.Order;
import it.yolo.v3.domain.Policy;
import it.yolo.v3.domain.Product;
import io.smallrye.mutiny.Uni;
import jakarta.enterprise.context.ApplicationScoped;
import org.jboss.logging.Logger;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * Servizio di dominio per la gestione delle polizze.
 * Contiene la logica di business per l'emissione delle polizze.
 * 
 * Implementazione temporanea per il Task 3.
 * Nel Task 5 sarà sostituita dal Strategy Pattern.
 */
@ApplicationScoped
public class PolicyService {
    
    private static final Logger LOG = Logger.getLogger(PolicyService.class);
    
    /**
     * Emette una polizza basata sui dati dell'ordine, cliente e prodotto.
     * 
     * Questa è la logica critica che deve essere eseguita in modo sincrono.
     * Non include operazioni secondarie come generazione certificato o invio email.
     * 
     * @param order Dati dell'ordine
     * @param customer Dati del cliente
     * @param product Dati del prodotto
     * @return Uni con la polizza emessa
     */
    public Uni<Policy> emit(Order order, Customer customer, Product product) {
        LOG.infof("Starting policy emission for order %s, customer %s, product %s", 
                 order.orderCode(), customer.id(), product.id());
        
        return Uni.createFrom().item(() -> {
            // Validazioni di business
            validateEmissionData(order, customer, product);
            
            // Simula la logica di emissione
            // TODO: Nel Task 5 questo sarà delegato al Strategy Pattern
            String policyNumber = generatePolicyNumber(order, product);
            
            // Simula una breve elaborazione (es. chiamata a sistema esterno)
            try {
                Thread.sleep(200); // Simula latenza del sistema di emissione
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                throw new RuntimeException("Emission interrupted", e);
            }
            
            Policy policy = new Policy(
                null, // ID sarà assegnato dal sistema
                policyNumber,
                order.orderCode(),
                customer.id(),
                product.id(),
                "EMITTED",
                product.price() != null ? product.price() : BigDecimal.ZERO,
                LocalDateTime.now(), // Start date
                LocalDateTime.now().plusYears(1), // End date (1 anno)
                LocalDateTime.now() // Emission date
            );
            
            LOG.infof("Policy emitted successfully: %s for order %s", 
                     policyNumber, order.orderCode());
            
            return policy;
        });
    }
    
    /**
     * Valida i dati necessari per l'emissione
     */
    private void validateEmissionData(Order order, Customer customer, Product product) {
        if (!order.isValidForEmission()) {
            throw new IllegalArgumentException("Order is not in a valid state for emission: " + order.state());
        }
        
        if (!customer.isValidForEmission()) {
            throw new IllegalArgumentException("Customer data is not valid for emission");
        }
        
        if (!product.isValidForEmission()) {
            throw new IllegalArgumentException("Product is not valid for emission");
        }
        
        if (!order.hasConfirmedPayment()) {
            throw new IllegalArgumentException("Order payment is not confirmed");
        }
    }
    
    /**
     * Genera il numero di polizza
     * 
     * TODO: Implementare la logica reale di generazione
     */
    private String generatePolicyNumber(Order order, Product product) {
        // Logica semplificata per il test
        String prefix = product.insuranceCompany().substring(0, Math.min(3, product.insuranceCompany().length())).toUpperCase();
        String timestamp = String.valueOf(System.currentTimeMillis() % 1000000);
        return prefix + order.orderCode() + timestamp;
    }
}
