package it.yolo.v3.resource;

import it.yolo.v3.dto.EmissionRequest;
import it.yolo.v3.dto.EmissionResponse;
import it.yolo.v3.dto.EmissionState;
import it.yolo.v3.dto.EmissionStatus;
import it.yolo.v3.repository.EmissionStatusRepository;
import it.yolo.v3.service.EmissionOrchestrator;
import io.smallrye.mutiny.Uni;
import org.eclipse.microprofile.openapi.annotations.Operation;
import org.eclipse.microprofile.openapi.annotations.media.Content;
import org.eclipse.microprofile.openapi.annotations.media.Schema;
import org.eclipse.microprofile.openapi.annotations.responses.APIResponse;
import org.eclipse.microprofile.openapi.annotations.responses.APIResponses;
import org.eclipse.microprofile.openapi.annotations.tags.Tag;
import org.jboss.logging.Logger;
import org.jboss.resteasy.reactive.RestResponse;

import javax.inject.Inject;
import javax.ws.rs.*;
import javax.ws.rs.core.MediaType;
import javax.ws.rs.core.Response;
import javax.ws.rs.core.UriBuilder;
import java.net.URI;
import java.time.Instant;

/**
 * Endpoint REST V3 per l'emissione asincrona delle polizze.
 * 
 * Caratteristiche:
 * - Completamente reattivo (usa Uni<T>)
 * - Risposta immediata < 500ms
 * - Elaborazione asincrona in background
 * - Tracciamento dello stato del processo
 */
@Path("/api/v3/emissions")
@Produces(MediaType.APPLICATION_JSON)
@Consumes(MediaType.APPLICATION_JSON)
@Tag(name = "Emission V3", description = "API asincrona per l'emissione delle polizze")
public class EmissionResourceV3 {
    
    private static final Logger LOG = Logger.getLogger(EmissionResourceV3.class);
    
    @Inject
    EmissionOrchestrator orchestrator;
    
    @Inject
    EmissionStatusRepository statusRepository;
    
    /**
     * Avvia il processo di emissione asincrono per un ordine.
     * 
     * Fase sincrona (< 500ms):
     * - Validazione input
     * - Recupero dati essenziali (Ordine, Cliente, Prodotto) in parallelo
     * - Emissione della polizza
     * - Risposta 201 Created al client
     * 
     * Fase asincrona (fire-and-forget):
     * - Generazione certificato
     * - Invio email
     * - Upload documenti
     * - Aggiornamento finale ordine
     */
    @POST
    @Path("/{orderCode}")
    @Operation(
        summary = "Avvia l'emissione asincrona di una polizza",
        description = "Emette una polizza e avvia le operazioni post-emissione in background"
    )
    @APIResponses({
        @APIResponse(
            responseCode = "201",
            description = "Emissione avviata con successo",
            content = @Content(schema = @Schema(implementation = EmissionResponse.class))
        ),
        @APIResponse(responseCode = "400", description = "Richiesta non valida"),
        @APIResponse(responseCode = "401", description = "Token di autorizzazione non valido"),
        @APIResponse(responseCode = "404", description = "Ordine non trovato"),
        @APIResponse(responseCode = "409", description = "Polizza già emessa per questo ordine"),
        @APIResponse(responseCode = "422", description = "Dati dell'ordine non validi per l'emissione"),
        @APIResponse(responseCode = "500", description = "Errore interno del server")
    })
    public Uni<RestResponse<EmissionResponse>> emit(
            @PathParam("orderCode") String orderCode,
            @HeaderParam("Authorization") String token,
            EmissionRequest request) {
        
        LOG.infof("Received emission request for order: %s", orderCode);
        
        // Validazione input
        if (orderCode == null || orderCode.isBlank()) {
            return Uni.createFrom().item(
                RestResponse.status(Response.Status.BAD_REQUEST, "Order code is required")
            );
        }
        
        if (token == null || token.isBlank()) {
            return Uni.createFrom().item(
                RestResponse.status(Response.Status.UNAUTHORIZED, "Authorization token is required")
            );
        }
        
        // Inizializza lo stato del processo
        EmissionStatus initialStatus = new EmissionStatus(
            orderCode, 
            null, 
            EmissionState.PENDING, 
            "Emission process started", 
            Instant.now()
        );
        
        return statusRepository.save(initialStatus)
            .flatMap(savedStatus -> {
                LOG.infof("Initialized emission status for order %s", orderCode);
                
                // Avvia il processo di emissione
                return orchestrator.startEmissionProcess(orderCode, token);
            })
            .map(emissionResponse -> {
                LOG.infof("Emission completed for order %s, policy: %s", 
                         orderCode, emissionResponse.policyNumber());
                
                // Aggiorna lo stato con il numero di polizza
                statusRepository.updatePolicyNumber(orderCode, emissionResponse.policyNumber())
                    .flatMap(status -> statusRepository.updateStatus(orderCode, EmissionState.EMITTED, 
                                                                    "Policy emitted, background tasks started"))
                    .subscribe().with(
                        status -> LOG.debugf("Status updated to EMITTED for order %s", orderCode),
                        failure -> LOG.errorf(failure, "Failed to update status for order %s", orderCode)
                    );
                
                // Crea l'URI per il Location header
                URI statusLocation = UriBuilder.fromPath("/api/v3/emissions/{orderCode}/status")
                    .build(orderCode);
                
                // Restituisce 201 Created con Location header
                return RestResponse.created(statusLocation, emissionResponse);
            })
            .onFailure().recoverWithItem(throwable -> {
                LOG.errorf(throwable, "Emission failed for order %s", orderCode);
                
                // Aggiorna lo stato a FAILED
                statusRepository.updateStatus(orderCode, EmissionState.FAILED, throwable.getMessage())
                    .subscribe().with(
                        status -> LOG.debugf("Status updated to FAILED for order %s", orderCode),
                        failure -> LOG.errorf(failure, "Failed to update failed status for order %s", orderCode)
                    );
                
                // Determina il codice di errore appropriato
                if (throwable instanceof IllegalArgumentException) {
                    return RestResponse.status(Response.Status.BAD_REQUEST, throwable.getMessage());
                } else if (throwable instanceof NotFoundException) {
                    return RestResponse.status(Response.Status.NOT_FOUND, throwable.getMessage());
                } else {
                    return RestResponse.status(Response.Status.INTERNAL_SERVER_ERROR, 
                                             "Internal server error during emission");
                }
            });
    }
    
    /**
     * Recupera lo stato del processo di emissione per un ordine.
     */
    @GET
    @Path("/{orderCode}/status")
    @Operation(
        summary = "Recupera lo stato del processo di emissione",
        description = "Restituisce lo stato attuale del processo di emissione asincrono"
    )
    @APIResponses({
        @APIResponse(
            responseCode = "200",
            description = "Stato recuperato con successo",
            content = @Content(schema = @Schema(implementation = EmissionStatus.class))
        ),
        @APIResponse(responseCode = "404", description = "Stato non trovato per l'ordine specificato")
    })
    public Uni<RestResponse<EmissionStatus>> getStatus(@PathParam("orderCode") String orderCode) {
        
        LOG.debugf("Retrieving emission status for order: %s", orderCode);
        
        if (orderCode == null || orderCode.isBlank()) {
            return Uni.createFrom().item(
                RestResponse.status(Response.Status.BAD_REQUEST, "Order code is required")
            );
        }
        
        return statusRepository.findByOrderCode(orderCode)
            .map(status -> {
                if (status == null) {
                    LOG.warnf("Emission status not found for order: %s", orderCode);
                    return RestResponse.status(Response.Status.NOT_FOUND, 
                                             "Emission status not found for order: " + orderCode);
                } else {
                    LOG.debugf("Retrieved emission status for order %s: %s", orderCode, status.state());
                    return RestResponse.ok(status);
                }
            });
    }
}
