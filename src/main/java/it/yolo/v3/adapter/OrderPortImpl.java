package it.yolo.v3.adapter;

import it.yolo.v3.domain.Order;
import it.yolo.v3.port.OrderPort;
import io.smallrye.mutiny.Uni;
import jakarta.enterprise.context.ApplicationScoped;
import org.jboss.logging.Logger;

import java.time.LocalDateTime;

/**
 * Implementazione stub del OrderPort per il Task 3.
 * 
 * In una implementazione reale, questa classe userebbe i client REST
 * esistenti (OrderClient) per recuperare i dati dal servizio Order.
 * 
 * TODO: Implementare la logica reale usando i client esistenti
 */
@ApplicationScoped
public class OrderPortImpl implements OrderPort {
    
    private static final Logger LOG = Logger.getLogger(OrderPortImpl.class);
    
    @Override
    public Uni<Order> findByCode(String orderCode, String token) {
        LOG.infof("Retrieving order by code: %s", orderCode);
        
        return Uni.createFrom().item(() -> {
            // Simula una chiamata al servizio Order
            try {
                Thread.sleep(50); // Simula latenza di rete
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                throw new RuntimeException("Order retrieval interrupted", e);
            }
            
            // Crea un ordine mock per il testing
            Order mockOrder = new Order(
                "123",
                orderCode,
                "CUST001", // customerId
                "PROD001", // productId
                "Confirmed",
                "payment_token_123",
                "CREDIT_CARD",
                "txn_123456",
                LocalDateTime.now().minusHours(1),
                LocalDateTime.now()
            );
            
            LOG.infof("Order retrieved: %s", orderCode);
            return mockOrder;
        });
    }
    
    @Override
    public Uni<Order> updateState(String orderCode, String newState, String token) {
        LOG.infof("Updating order %s state to: %s", orderCode, newState);
        
        return findByCode(orderCode, token)
            .map(order -> new Order(
                order.id(),
                order.orderCode(),
                order.customerId(),
                order.productId(),
                newState,
                order.paymentToken(),
                order.paymentType(),
                order.paymentTransactionId(),
                order.createdAt(),
                LocalDateTime.now()
            ));
    }
    
    @Override
    public Uni<Order> markAsComplete(String orderCode, String token) {
        return updateState(orderCode, "COMPLETE", token);
    }
    
    @Override
    public Uni<Order> markAsFailed(String orderCode, String errorMessage, String token) {
        LOG.errorf("Marking order %s as failed: %s", orderCode, errorMessage);
        return updateState(orderCode, "FAILED", token);
    }
}
