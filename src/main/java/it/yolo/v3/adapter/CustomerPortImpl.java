package it.yolo.v3.adapter;

import it.yolo.client.customer.CustomerClient;
import it.yolo.v3.domain.Customer;
import it.yolo.v3.port.CustomerPort;
import io.smallrye.mutiny.Uni;
import org.eclipse.microprofile.rest.client.inject.RestClient;
import org.jboss.logging.Logger;

import javax.enterprise.context.ApplicationScoped;
import javax.inject.Inject;
import javax.ws.rs.NotFoundException;
import javax.ws.rs.core.Response;
import java.util.concurrent.CompletionException;

@ApplicationScoped
public class CustomerPortImpl implements CustomerPort {

    @Inject
    @RestClient
    CustomerClient customerClient;

    private static final Logger LOG = Logger.getLogger(CustomerPortImpl.class);

    @Override
    public Uni<Customer> findById(String customerId, String token) {
        LOG.infof("Retrieving customer by ID: %s", customerId);

        return Uni.createFrom().item(() -> {
                    try {
                        Long id = Long.parseLong(customerId);
                        return customerClient.findById(token, id);
                    } catch (NumberFormatException e) {
                        throw new IllegalArgumentException(
                                String.format("Invalid customer ID format: '%s'. Must be a numeric value", customerId), e);
                    }
                }).flatMap(response -> {
                    try {
                        if (response.getStatus() == Response.Status.OK.getStatusCode()) {
                            Customer customer = response.readEntity(Customer.class);
                            LOG.infof("Customer retrieved: %s", customerId);
                            return Uni.createFrom().item(customer);
                        } else if (response.getStatus() == Response.Status.NOT_FOUND.getStatusCode()) {
                            LOG.warnf("Customer not found for ID: %s", customerId);
                            return Uni.createFrom().failure(new NotFoundException("Customer not found"));
                        } else {
                            String errorMsg = String.format(
                                    "Customer service error [ID=%s]: HTTP %d",
                                    customerId, response.getStatus()
                            );
                            LOG.error(errorMsg);
                            return Uni.createFrom().failure(
                                    new RuntimeException(errorMsg)
                            );
                        }
                    } finally {
                        response.close();
                    }
                }).onFailure(IllegalArgumentException.class)
                .recoverWithUni(failure -> Uni.createFrom().failure(failure))
                .onFailure(NotFoundException.class)
                .recoverWithUni(failure -> Uni.createFrom().failure(failure))
                .onFailure()
                .recoverWithUni(failure -> {
                    Throwable rootCause = failure instanceof CompletionException ?
                            failure.getCause() : failure;

                    LOG.errorf("Unexpected error retrieving customer ID %s: %s",
                            customerId, rootCause.getMessage());

                    return Uni.createFrom().failure(
                            new RuntimeException("Customer service unavailable", rootCause)
                    );
                });
    }

    @Override
    public Uni<Customer> findByTaxCode(String taxCode, String token) {
        LOG.infof("Retrieving customer by tax code: %s", taxCode);

        return Uni.createFrom().item(() ->
                        customerClient.findByNdg(token, taxCode)
                ).flatMap(response -> {
                    try {
                        if (response.getStatus() == Response.Status.OK.getStatusCode()) {
                            Customer customer = response.readEntity(Customer.class);
                            LOG.infof("Customer retrieved by tax code: %s", taxCode);
                            return Uni.createFrom().item(customer);
                        } else if (response.getStatus() == Response.Status.NOT_FOUND.getStatusCode()) {
                            LOG.warnf("Customer not found for tax code: %s", taxCode);
                            return Uni.createFrom().failure(new NotFoundException("Customer not found"));
                        } else {
                            String errorMsg = String.format(
                                    "Customer service error [TAX=%s]: HTTP %d",
                                    taxCode, response.getStatus()
                            );
                            LOG.error(errorMsg);
                            return Uni.createFrom().failure(
                                    new RuntimeException(errorMsg)
                            );
                        }
                    } finally {
                        response.close();
                    }
                }).onFailure(NotFoundException.class)
                .recoverWithUni(failure -> Uni.createFrom().failure(failure))
                .onFailure()
                .recoverWithUni(failure -> {
                    LOG.errorf("Error retrieving customer by tax code %s: %s",
                            taxCode, failure.getMessage());

                    return Uni.createFrom().failure(
                            new RuntimeException("Customer service unavailable", failure)
                    );
                });
    }

    @Override
    public Uni<Customer> findByNdg(String ndg, String token) {
        LOG.infof("Retrieving customer by NDG: %s", ndg);

        return Uni.createFrom().item(() ->
                        customerClient.findByNdg(token, ndg)
                ).flatMap(response -> {
                    try {
                        if (response.getStatus() == Response.Status.OK.getStatusCode()) {
                            Customer customer = response.readEntity(Customer.class);
                            LOG.infof("Customer retrieved by NDG: %s", ndg);
                            return Uni.createFrom().item(customer);
                        } else if (response.getStatus() == Response.Status.NOT_FOUND.getStatusCode()) {
                            LOG.warnf("Customer not found for NDG: %s", ndg);
                            return Uni.createFrom().failure(new NotFoundException("Customer not found"));
                        } else {
                            String errorMsg = String.format(
                                    "Customer service error [NDG=%s]: HTTP %d",
                                    ndg, response.getStatus()
                            );
                            LOG.error(errorMsg);
                            return Uni.createFrom().failure(
                                    new RuntimeException(errorMsg)
                            );
                        }
                    } finally {
                        response.close();
                    }
                }).onFailure(NotFoundException.class)
                .recoverWithUni(failure -> Uni.createFrom().failure(failure))
                .onFailure()
                .recoverWithUni(failure -> {
                    LOG.errorf("Error retrieving customer by NDG %s: %s",
                            ndg, failure.getMessage());

                    return Uni.createFrom().failure(
                            new RuntimeException("Customer service unavailable", failure)
                    );
                });
    }
}