package it.yolo.v3.adapter;

import it.yolo.v3.domain.Customer;
import it.yolo.v3.port.CustomerPort;
import io.smallrye.mutiny.Uni;
import jakarta.enterprise.context.ApplicationScoped;
import org.jboss.logging.Logger;

/**
 * Implementazione stub del CustomerPort per il Task 3.
 * 
 * In una implementazione reale, questa classe userebbe i client REST
 * esistenti (CustomerClient) per recuperare i dati dal servizio Customer.
 * 
 * TODO: Implementare la logica reale usando i client esistenti
 */
@ApplicationScoped
public class CustomerPortImpl implements CustomerPort {
    
    private static final Logger LOG = Logger.getLogger(CustomerPortImpl.class);
    
    @Override
    public Uni<Customer> findById(String customerId, String token) {
        LOG.infof("Retrieving customer by ID: %s", customerId);
        
        return Uni.createFrom().item(() -> {
            // Simula una chiamata al servizio Customer
            try {
                Thread.sleep(30); // Simula latenza di rete
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                throw new RuntimeException("Customer retrieval interrupted", e);
            }
            
            // Crea un cliente mock per il testing
            Customer mockCustomer = new Customer(
                customerId,
                "CUST" + customerId,
                "Mario",
                "Rossi",
                "RSSMRA80A01H501Z",
                "<EMAIL>",
                "+39 123 456 7890",
                "Via Roma 123",
                "Milano",
                "20100",
                "MI"
            );
            
            LOG.infof("Customer retrieved: %s", customerId);
            return mockCustomer;
        });
    }
    
    @Override
    public Uni<Customer> findByTaxCode(String taxCode, String token) {
        LOG.infof("Retrieving customer by tax code: %s", taxCode);
        
        return Uni.createFrom().item(() -> {
            // Simula una chiamata al servizio Customer
            try {
                Thread.sleep(30);
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                throw new RuntimeException("Customer retrieval interrupted", e);
            }
            
            Customer mockCustomer = new Customer(
                "CUST001",
                "CUST001",
                "Mario",
                "Rossi",
                taxCode,
                "<EMAIL>",
                "+39 123 456 7890",
                "Via Roma 123",
                "Milano",
                "20100",
                "MI"
            );
            
            LOG.infof("Customer retrieved by tax code: %s", taxCode);
            return mockCustomer;
        });
    }
    
    @Override
    public Uni<Customer> findByNdg(String ndg, String token) {
        LOG.infof("Retrieving customer by NDG: %s", ndg);
        
        return Uni.createFrom().item(() -> {
            // Simula una chiamata al servizio Customer
            try {
                Thread.sleep(30);
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                throw new RuntimeException("Customer retrieval interrupted", e);
            }
            
            Customer mockCustomer = new Customer(
                "CUST001",
                "CUST001",
                "Mario",
                "Rossi",
                "RSSMRA80A01H501Z",
                "<EMAIL>",
                "+39 123 456 7890",
                "Via Roma 123",
                "Milano",
                "20100",
                "MI"
            );
            
            LOG.infof("Customer retrieved by NDG: %s", ndg);
            return mockCustomer;
        });
    }
}
