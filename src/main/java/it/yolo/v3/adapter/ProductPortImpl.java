package it.yolo.v3.adapter;

import it.yolo.v3.domain.Product;
import it.yolo.v3.port.ProductPort;
import io.smallrye.mutiny.Uni;
import jakarta.enterprise.context.ApplicationScoped;
import org.jboss.logging.Logger;

import java.math.BigDecimal;

/**
 * Implementazione stub del ProductPort per il Task 3.
 * 
 * In una implementazione reale, questa classe userebbe i client REST
 * esistenti (ProductClient) per recuperare i dati dal servizio Product.
 * 
 * TODO: Implementare la logica reale usando i client esistenti
 */
@ApplicationScoped
public class ProductPortImpl implements ProductPort {
    
    private static final Logger LOG = Logger.getLogger(ProductPortImpl.class);
    
    @Override
    public Uni<Product> findById(String productId, String token) {
        LOG.infof("Retrieving product by ID: %s", productId);
        
        return Uni.createFrom().item(() -> {
            // Simula una chiamata al servizio Product
            try {
                Thread.sleep(40); // Simula latenza di rete
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                throw new RuntimeException("Product retrieval interrupted", e);
            }
            
            // Crea un prodotto mock per il testing
            Product mockProduct = new Product(
                productId,
                "TRAVEL_BASIC",
                "Assicurazione Viaggio Base",
                "YOLO_INSURANCE",
                new BigDecimal("29.99"),
                "INTERNAL", // Tipo di emissione
                "STANDARD",
                "TRAVEL"
            );
            
            LOG.infof("Product retrieved: %s", productId);
            return mockProduct;
        });
    }
    
    @Override
    public Uni<Product> findByCode(String productCode, String token) {
        LOG.infof("Retrieving product by code: %s", productCode);
        
        return Uni.createFrom().item(() -> {
            // Simula una chiamata al servizio Product
            try {
                Thread.sleep(40);
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                throw new RuntimeException("Product retrieval interrupted", e);
            }
            
            Product mockProduct = new Product(
                "PROD001",
                productCode,
                "Assicurazione Viaggio Base",
                "YOLO_INSURANCE",
                new BigDecimal("29.99"),
                "INTERNAL",
                "STANDARD",
                "TRAVEL"
            );
            
            LOG.infof("Product retrieved by code: %s", productCode);
            return mockProduct;
        });
    }
}
