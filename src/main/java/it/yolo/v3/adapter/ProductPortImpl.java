package it.yolo.v3.adapter;

import it.yolo.client.product.ProductClient;
import it.yolo.v3.domain.Product;
import it.yolo.v3.port.ProductPort;
import io.smallrye.mutiny.Uni;
import org.eclipse.microprofile.rest.client.inject.RestClient;
import org.jboss.logging.Logger;

import javax.enterprise.context.ApplicationScoped;
import javax.inject.Inject;

/**
 * Implementazione stub del ProductPort per il Task 3.
 * 
 * In una implementazione reale, questa classe userebbe i client REST
 * esistenti (ProductClient) per recuperare i dati dal servizio Product.
 * 
 * TODO: Implementare la logica reale usando i client esistenti
 */
@ApplicationScoped
public class ProductPortImpl implements ProductPort {

    @RestClient
    @Inject
    ProductClient productClient;

    private static final Logger LOG = Logger.getLogger(ProductPortImpl.class);
    
    @Override
    public Uni<Product> findById(String productId, String token) {
        LOG.infof("Retrieving product by ID: %s", productId);
        
        return Uni.createFrom().item(() -> {
            LOG.infof("Product retrieved: %s", productId);
            return  productClient.findById(token, null, Long.valueOf(productId)).readEntity(Product.class);;
        });
    }
}
