package it.yolo.v3.strategy;

import it.yolo.v3.domain.Policy;
import io.smallrye.mutiny.Uni;
import org.eclipse.microprofile.config.inject.ConfigProperty;
import org.jboss.logging.Logger;

import javax.enterprise.context.ApplicationScoped;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * Strategia per l'emissione interna delle polizze.
 * 
 * Gestisce l'emissione di polizze che vengono create direttamente
 * dal sistema interno senza delegare a PG esterni.
 */
@ApplicationScoped
public class InternalEmissionStrategy implements EmissionStrategy {
    
    private static final Logger LOG = Logger.getLogger(InternalEmissionStrategy.class);
    
    @ConfigProperty(name = "emission.internal.policy-prefix", defaultValue = "INT")
    String policyPrefix;
    
    @Override
    public Uni<Policy> emit(EmissionContext context) {
        LOG.infof("Starting internal emission for order %s", context.order().data().orderCode());
        
        return Uni.createFrom().item(() -> {
            // Validazione specifica per emissione interna
            validateInternalEmission(context);
            
            // Simula la logica di emissione interna
            try {
                Thread.sleep(150); // Simula elaborazione interna
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                throw new RuntimeException("Internal emission interrupted", e);
            }
            
            // Genera il numero di polizza per emissione interna
            String policyNumber = generateInternalPolicyNumber(context);
            
            // Crea la polizza
            Policy policy = new Policy(
                null, // ID sarà assegnato dal sistema
                policyNumber,
                context.order().data().orderCode(),
                context.customer().data().id(),
                context.product().id(),
                "EMITTED",
                context.product().price() != null ? context.product().price() : BigDecimal.ZERO,
                LocalDateTime.now(), // Start date
                LocalDateTime.now().plusYears(1), // End date
                LocalDateTime.now() // Emission date
            );
            
            LOG.infof("Internal emission completed: policy %s for order %s", 
                     policyNumber, context.order().data().orderCode());
            
            return policy;
        });
    }
    
    @Override
    public String getStrategyName() {
        return "INTERNAL";
    }
    
    private void validateInternalEmission(EmissionContext context) {
        if (!context.isValidForEmission()) {
            throw new IllegalArgumentException("Context is not valid for internal emission");
        }
        
        if (!context.product().isInternalEmission()) {
            throw new IllegalArgumentException("Product is not configured for internal emission");
        }
        
        // Validazioni specifiche per emissione interna
        if (context.product().price() == null || context.product().price().compareTo(BigDecimal.ZERO) <= 0) {
            throw new IllegalArgumentException("Product price must be greater than zero for internal emission");
        }
    }
    
    private String generateInternalPolicyNumber(EmissionContext context) {
        // Formato: PREFIX + COMPANY_CODE + ORDER_CODE + TIMESTAMP
        String companyCode = context.product().insuranceCompany()
            .substring(0, Math.min(3, context.product().insuranceCompany().length()))
            .toUpperCase();
        
        String timestamp = String.valueOf(System.currentTimeMillis() % 1000000);
        
        return policyPrefix + companyCode + context.order().data().orderCode() + timestamp;
    }
}
