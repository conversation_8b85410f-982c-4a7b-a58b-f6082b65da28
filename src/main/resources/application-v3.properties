# Configurazione per l'API V3 di emissione

# Configurazioni generali
emission.v3.enabled=true
emission.v3.background.pool-size=50

# Configurazioni per emissione interna
emission.internal.policy-prefix=INT
emission.internal.timeout-ms=3000

# Configurazioni per emissione esterna
emission.external.policy-prefix=EXT
emission.external.timeout-ms=5000

# Configurazioni per Virtual Threads
quarkus.virtual-threads.enabled=true

# Configurazioni per il repository di stato (in-memory)
emission.status.cleanup-interval=3600
emission.status.max-entries=10000

# Configurazioni per i servizi di background
emission.background.certificate.timeout-ms=10000
emission.background.email.timeout-ms=8000
emission.background.document.timeout-ms=12000

# Configurazioni per retry logic (future enhancement)
emission.background.retry.max-attempts=3
emission.background.retry.delay-ms=1000

# Logging specifico per V3
quarkus.log.category."it.yolo.v3".level=DEBUG
